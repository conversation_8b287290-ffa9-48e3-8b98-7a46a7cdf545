#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قدرة Gemini 2.5 Pro على إنشاء الصور
"""

import asyncio
import sys
import os
import json
import aiohttp
import base64
from datetime import datetime
from typing import Dict, List, Optional, Any

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from config.settings import google_api_manager

class GeminiImageTester:
    """فئة اختبار قدرة Gemini 2.5 Pro على إنشاء الصور"""

    def __init__(self):
        self.enabled = bool(google_api_manager and google_api_manager.get_key())
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = "gemini-2.5-pro"

        # إعدادات الاختبار
        self.test_config = {
            'timeout': 60,
            'max_retries': 3,
            'retry_delay': 5
        }

        # نتائج الاختبار
        self.test_results = {
            'image_generation_supported': False,
            'text_to_image_available': False,
            'image_editing_available': False,
            'supported_formats': [],
            'error_messages': [],
            'test_timestamp': datetime.now().isoformat()
        }

        logger.info(f"🧪 تم تهيئة اختبار إنشاء الصور Gemini 2.5 Pro - الحالة: {'مفعل' if self.enabled else 'معطل'}")

    async def test_image_generation_capability(self) -> Dict[str, Any]:
        """اختبار قدرة إنشاء الصور"""

        if not self.enabled:
            logger.warning("⚠️ نظام Gemini غير مفعل")
            return self.test_results

        print("\n" + "="*60)
        print("🎨 اختبار قدرة Gemini 2.5 Pro على إنشاء الصور")
        print("="*60)

        # اختبارات متعددة
        test_cases = [
            {
                'name': 'اختبار إنشاء صورة بسيطة',
                'method': 'text_to_image',
                'prompt': 'Create a simple gaming controller image'
            },
            {
                'name': 'اختبار إنشاء صورة معقدة',
                'method': 'text_to_image',
                'prompt': 'Generate a futuristic gaming setup with RGB lighting and multiple monitors'
            },
            {
                'name': 'اختبار إنشاء صورة عربية',
                'method': 'text_to_image',
                'prompt': 'أنشئ صورة لوحة تحكم ألعاب حديثة'
            }
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 {i}. {test_case['name']}")
            print("-" * 40)

            try:
                result = await self._test_specific_generation_method(test_case)

                if result['success']:
                    print(f"   ✅ نجح: {result.get('message', 'تم إنشاء الصورة')}")
                    self.test_results['image_generation_supported'] = True
                    if test_case['method'] == 'text_to_image':
                        self.test_results['text_to_image_available'] = True
                else:
                    print(f"   ❌ فشل: {result.get('error', 'خطأ غير معروف')}")
                    self.test_results['error_messages'].append(f"{test_case['name']}: {result.get('error', 'خطأ غير معروف')}")

            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                self.test_results['error_messages'].append(f"{test_case['name']}: {str(e)}")

        # اختبار قدرات أخرى
        await self._test_model_capabilities()

        # عرض النتائج النهائية
        self._display_final_results()

        return self.test_results

    async def _test_specific_generation_method(self, test_case: Dict) -> Dict[str, Any]:
        """اختبار طريقة إنشاء محددة"""

        try:
            # محاولة إنشاء صورة باستخدام Gemini
            url = f"{self.base_url}/models/{self.model_name}:generateContent"

            headers = {
                'Content-Type': 'application/json'
            }

            params = {
                'key': google_api_manager.get_key()
            }

            # تجربة طلب إنشاء صورة
            payload = {
                "contents": [{
                    "parts": [{
                        "text": f"Generate an image: {test_case['prompt']}"
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.9,
                    "maxOutputTokens": 2048,
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, params=params, json=payload, timeout=self.test_config['timeout']) as response:

                    if response.status == 200:
                        data = await response.json()

                        # فحص الاستجابة للبحث عن صور
                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0].get('content', {})
                            parts = content.get('parts', [])

                            # البحث عن أجزاء تحتوي على صور
                            has_image = False
                            text_response = ""

                            for part in parts:
                                if 'text' in part:
                                    text_response += part['text']
                                elif 'inlineData' in part:
                                    # وجود بيانات مضمنة (قد تكون صورة)
                                    has_image = True
                                elif 'fileData' in part:
                                    # وجود ملف (قد يكون صورة)
                                    has_image = True

                            if has_image:
                                return {
                                    'success': True,
                                    'message': 'تم إنشاء صورة بنجاح',
                                    'has_image_data': True,
                                    'response_preview': text_response[:100] if text_response else 'صورة مُنشأة'
                                }
                            else:
                                # فحص ما إذا كان النص يشير إلى عدم القدرة على إنشاء الصور
                                if any(phrase in text_response.lower() for phrase in [
                                    "can't generate", "cannot create", "unable to generate",
                                    "لا أستطيع إنشاء", "لا يمكنني توليد", "غير قادر على إنشاء"
                                ]):
                                    return {
                                        'success': False,
                                        'error': 'النموذج لا يدعم إنشاء الصور',
                                        'response_text': text_response[:200]
                                    }
                                else:
                                    return {
                                        'success': False,
                                        'error': 'لم يتم إنشاء صورة، استجابة نصية فقط',
                                        'response_text': text_response[:200]
                                    }

                    elif response.status == 400:
                        error_data = await response.json()
                        error_message = error_data.get('error', {}).get('message', 'خطأ في الطلب')

                        if 'image generation' in error_message.lower() or 'not supported' in error_message.lower():
                            return {
                                'success': False,
                                'error': 'إنشاء الصور غير مدعوم في هذا النموذج',
                                'api_error': error_message
                            }
                        else:
                            return {
                                'success': False,
                                'error': f'خطأ في API: {error_message}',
                                'status_code': response.status
                            }

                    else:
                        error_text = await response.text()
                        return {
                            'success': False,
                            'error': f'خطأ HTTP {response.status}',
                            'details': error_text[:200]
                        }

        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': 'انتهت مهلة الطلب'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'خطأ في الاتصال: {str(e)}'
            }

    async def _test_model_capabilities(self):
        """اختبار قدرات النموذج العامة"""

        print(f"\n🔍 اختبار قدرات النموذج العامة...")

        try:
            # الحصول على معلومات النموذج
            url = f"{self.base_url}/models/{self.model_name}"
            params = {'key': google_api_manager.get_key()}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        model_info = await response.json()

                        # فحص القدرات المدعومة
                        supported_generation_methods = model_info.get('supportedGenerationMethods', [])
                        input_token_limit = model_info.get('inputTokenLimit', 0)
                        output_token_limit = model_info.get('outputTokenLimit', 0)

                        print(f"   📊 طرق التوليد المدعومة: {supported_generation_methods}")
                        print(f"   📊 حد الرموز المدخلة: {input_token_limit:,}")
                        print(f"   📊 حد الرموز المخرجة: {output_token_limit:,}")

                        # فحص دعم إنشاء الصور
                        if 'generateContent' in supported_generation_methods:
                            print(f"   ✅ يدعم توليد المحتوى")

                        # حفظ المعلومات
                        self.test_results['supported_formats'] = supported_generation_methods
                        self.test_results['input_token_limit'] = input_token_limit
                        self.test_results['output_token_limit'] = output_token_limit

                    else:
                        print(f"   ❌ فشل في الحصول على معلومات النموذج: {response.status}")

        except Exception as e:
            print(f"   ❌ خطأ في اختبار قدرات النموذج: {e}")

    def _display_final_results(self):
        """عرض النتائج النهائية"""

        print(f"\n📈 ملخص نتائج الاختبار:")
        print("=" * 50)

        print(f"🎨 دعم إنشاء الصور: {'✅ نعم' if self.test_results['image_generation_supported'] else '❌ لا'}")
        print(f"📝 تحويل النص إلى صورة: {'✅ نعم' if self.test_results['text_to_image_available'] else '❌ لا'}")
        print(f"🖼️ تحرير الصور: {'✅ نعم' if self.test_results['image_editing_available'] else '❌ لا'}")

        if self.test_results['supported_formats']:
            print(f"📋 الطرق المدعومة: {', '.join(self.test_results['supported_formats'])}")

        if self.test_results['error_messages']:
            print(f"\n❌ الأخطاء المكتشفة:")
            for i, error in enumerate(self.test_results['error_messages'], 1):
                print(f"   {i}. {error}")

        # التوصيات
        print(f"\n💡 التوصيات:")
        if not self.test_results['image_generation_supported']:
            print("   • Gemini 2.5 Pro لا يدعم إنشاء الصور حالياً")
            print("   • يمكن استخدام النظام الحالي لإدارة الصور (image_guard.py)")
            print("   • النظام يدعم APIs خارجية لإنشاء الصور (Pexels, Pixabay, etc.)")
        else:
            print("   • يمكن تطوير نظام إنشاء صور باستخدام Gemini")
            print("   • يُنصح بدمج هذه القدرة مع النظام الحالي")

async def main():
    """الدالة الرئيسية"""
    try:
        tester = GeminiImageTester()

        if not tester.enabled:
            print("❌ لا يمكن تشغيل الاختبار - تحقق من مفتاح API")
            return

        # تشغيل الاختبار
        results = await tester.test_image_generation_capability()

        # حفظ النتائج
        report_file = f"gemini_image_generation_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 تم حفظ التقرير في: {report_file}")

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في التشغيل: {e}")

if __name__ == "__main__":
    asyncio.run(main())