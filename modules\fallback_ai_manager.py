# مدير النماذج الاحتياطية للبحث العميق
import asyncio
import aiohttp
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass
import logging

from .logger import logger
from config.settings import BotConfig

class AIModelType(Enum):
    """أنواع نماذج الذكاء الاصطناعي المتاحة"""
    GEMINI_2_FLASH = "gemini_2_flash"      # الأولوية الأولى - مجاني مع بحث
    DEEPSEEK_R1 = "deepseek_r1"            # الأولوية الثانية - تفكير عميق + بحث
    GROQ_API = "groq_api"                  # الأولوية الثالثة - سرعة عالية
    GEMINI_1_5_FLASH = "gemini_1_5_flash"  # الحل الاحتياطي النهائي - مجاني

@dataclass
class AIModelConfig:
    """تكوين نموذج الذكاء الاصطناعي"""
    name: str
    api_endpoint: str
    api_key: str
    max_requests_per_day: int
    max_requests_per_minute: int
    supports_web_search: bool
    cost_per_request: float
    quality_score: int  # من 1-10
    speed_score: int    # من 1-10
    
@dataclass
class SearchRequest:
    """طلب بحث للنماذج الاحتياطية"""
    query: str
    max_results: int = 10
    search_depth: str = "standard"  # standard, deep, comprehensive
    include_web_search: bool = True
    language: str = "ar"
    
class FallbackAIManager:
    """مدير النماذج الاحتياطية للبحث العميق"""
    
    def __init__(self):
        self.models = self._initialize_models()
        self.usage_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'models_used': {},
            'daily_usage': {},
            'last_reset': datetime.now().date()
        }
        self.model_status = {}
        self._initialize_model_status()
        
    def _initialize_models(self) -> Dict[AIModelType, AIModelConfig]:
        """تهيئة تكوينات النماذج"""
        return {
            AIModelType.GEMINI_2_FLASH: AIModelConfig(
                name="Gemini 2.5 Pro",  # تحديث إلى 2.5 Pro
                api_endpoint="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent",
                api_key=getattr(BotConfig, 'GEMINI_API_KEY', ''),  # استخدام نفس مفتاح Gemini الرئيسي
                max_requests_per_day=500,
                max_requests_per_minute=15,
                supports_web_search=True,
                cost_per_request=0.0,  # مجاني
                quality_score=10,  # أعلى جودة
                speed_score=8
            ),
            AIModelType.DEEPSEEK_R1: AIModelConfig(
                name="DeepSeek R1",
                api_endpoint="https://api.deepseek.com/v1/chat/completions",
                api_key=getattr(BotConfig, 'DEEPSEEK_API_KEY', ''),
                max_requests_per_day=1000,
                max_requests_per_minute=20,
                supports_web_search=True,
                cost_per_request=0.0,  # مجاني مع حدود
                quality_score=10,
                speed_score=7
            ),
            AIModelType.GROQ_API: AIModelConfig(
                name="Groq API",
                api_endpoint="https://api.groq.com/openai/v1/chat/completions",
                api_key=getattr(BotConfig, 'GROQ_API_KEY', ''),
                max_requests_per_day=2000,
                max_requests_per_minute=30,
                supports_web_search=True,
                cost_per_request=0.0,  # مجاني
                quality_score=8,
                speed_score=10
            ),
            AIModelType.GEMINI_1_5_FLASH: AIModelConfig(
                name="Gemini 2.5 Pro Backup",  # تحديث إلى 2.5 Pro كاحتياط
                api_endpoint="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent",
                api_key=getattr(BotConfig, 'GEMINI_API_KEY', ''),  # استخدام نفس مفتاح Gemini الرئيسي
                max_requests_per_day=1500,
                max_requests_per_minute=15,
                supports_web_search=True,  # دعم البحث الكامل
                cost_per_request=0.0,  # مجاني
                quality_score=10,  # أعلى جودة
                speed_score=9
            )
        }
    
    def _initialize_model_status(self):
        """تهيئة حالة النماذج"""
        for model_type in AIModelType:
            self.model_status[model_type] = {
                'available': True,
                'last_used': None,
                'consecutive_failures': 0,
                'daily_requests': 0,
                'minute_requests': 0,
                'last_minute_reset': datetime.now(),
                'blacklisted_until': None
            }
    
    async def search_with_fallback(self, request: SearchRequest) -> Optional[Dict]:
        """البحث مع النماذج الاحتياطية"""
        self._reset_daily_stats_if_needed()
        
        # ترتيب النماذج حسب الأولوية والتوفر
        available_models = self._get_available_models_by_priority()
        
        if not available_models:
            logger.error("❌ لا توجد نماذج متاحة للبحث")
            return None
        
        logger.info(f"🔍 بدء البحث الاحتياطي مع {len(available_models)} نموذج متاح")
        
        for model_type in available_models:
            try:
                logger.info(f"🤖 محاولة البحث باستخدام {self.models[model_type].name}")
                
                result = await self._search_with_model(model_type, request)
                
                if result and result.get('success'):
                    self._record_successful_request(model_type)
                    logger.info(f"✅ نجح البحث باستخدام {self.models[model_type].name}")
                    return result
                else:
                    self._record_failed_request(model_type)
                    logger.warning(f"⚠️ فشل البحث باستخدام {self.models[model_type].name}")
                    
            except Exception as e:
                self._record_failed_request(model_type)
                logger.error(f"❌ خطأ في البحث باستخدام {self.models[model_type].name}: {e}")
                
                # إذا كان الخطأ متعلق بالحدود، قم بتعطيل النموذج مؤقتاً
                if "rate limit" in str(e).lower() or "quota" in str(e).lower():
                    self._temporarily_blacklist_model(model_type, hours=1)
                
                continue
        
        logger.error("❌ فشلت جميع النماذج الاحتياطية في البحث")
        return None
    
    def _get_available_models_by_priority(self) -> List[AIModelType]:
        """الحصول على النماذج المتاحة مرتبة حسب الأولوية"""
        available = []
        
        for model_type in [AIModelType.GEMINI_2_FLASH, AIModelType.DEEPSEEK_R1, 
                          AIModelType.GROQ_API, AIModelType.GEMINI_1_5_FLASH]:
            if self._is_model_available(model_type):
                available.append(model_type)
        
        return available
    
    def _is_model_available(self, model_type: AIModelType) -> bool:
        """فحص توفر النموذج"""
        status = self.model_status[model_type]
        config = self.models[model_type]
        
        # فحص القائمة السوداء
        if status['blacklisted_until'] and datetime.now() < status['blacklisted_until']:
            return False
        
        # فحص الحدود اليومية
        if status['daily_requests'] >= config.max_requests_per_day:
            return False
        
        # فحص الحدود الدقيقية
        if self._check_minute_limit(model_type):
            return False
        
        # فحص الفشل المتتالي
        if status['consecutive_failures'] >= 3:
            return False
        
        # فحص وجود المفتاح
        if not config.api_key:
            return False
        
        return True
    
    def _check_minute_limit(self, model_type: AIModelType) -> bool:
        """فحص حد الطلبات في الدقيقة"""
        status = self.model_status[model_type]
        config = self.models[model_type]
        
        now = datetime.now()
        if (now - status['last_minute_reset']).total_seconds() >= 60:
            status['minute_requests'] = 0
            status['last_minute_reset'] = now
        
        return status['minute_requests'] >= config.max_requests_per_minute
    
    def _reset_daily_stats_if_needed(self):
        """إعادة تعيين الإحصائيات اليومية إذا لزم الأمر"""
        today = datetime.now().date()
        if self.usage_stats['last_reset'] != today:
            self.usage_stats['last_reset'] = today
            self.usage_stats['daily_usage'] = {}
            
            # إعادة تعيين العدادات اليومية للنماذج
            for model_type in AIModelType:
                self.model_status[model_type]['daily_requests'] = 0
                self.model_status[model_type]['consecutive_failures'] = 0
                
            logger.info("🔄 تم إعادة تعيين الإحصائيات اليومية")
    
    def _temporarily_blacklist_model(self, model_type: AIModelType, hours: int = 1):
        """تعطيل النموذج مؤقتاً"""
        blacklist_until = datetime.now() + timedelta(hours=hours)
        self.model_status[model_type]['blacklisted_until'] = blacklist_until
        logger.warning(f"⚠️ تم تعطيل {self.models[model_type].name} مؤقتاً حتى {blacklist_until}")
    
    def _record_successful_request(self, model_type: AIModelType):
        """تسجيل طلب ناجح"""
        self.usage_stats['total_requests'] += 1
        self.usage_stats['successful_requests'] += 1
        self.usage_stats['models_used'][model_type.value] = self.usage_stats['models_used'].get(model_type.value, 0) + 1
        
        status = self.model_status[model_type]
        status['last_used'] = datetime.now()
        status['consecutive_failures'] = 0
        status['daily_requests'] += 1
        status['minute_requests'] += 1
    
    def _record_failed_request(self, model_type: AIModelType):
        """تسجيل طلب فاشل"""
        self.usage_stats['total_requests'] += 1
        self.usage_stats['failed_requests'] += 1
        
        status = self.model_status[model_type]
        status['consecutive_failures'] += 1
        status['daily_requests'] += 1
        status['minute_requests'] += 1
    
    def get_usage_stats(self) -> Dict:
        """الحصول على إحصائيات الاستخدام"""
        return {
            **self.usage_stats,
            'model_status': {
                model_type.value: {
                    'available': self._is_model_available(model_type),
                    'daily_requests': self.model_status[model_type]['daily_requests'],
                    'consecutive_failures': self.model_status[model_type]['consecutive_failures'],
                    'last_used': self.model_status[model_type]['last_used']
                }
                for model_type in AIModelType
            }
        }

    async def _search_with_model(self, model_type: AIModelType, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام نموذج محدد"""
        config = self.models[model_type]

        if model_type == AIModelType.GEMINI_2_FLASH:
            return await self._search_with_gemini_2_flash(config, request)
        elif model_type == AIModelType.DEEPSEEK_R1:
            return await self._search_with_deepseek_r1(config, request)
        elif model_type == AIModelType.GROQ_API:
            return await self._search_with_groq(config, request)
        elif model_type == AIModelType.GEMINI_1_5_FLASH:
            return await self._search_with_gemini_1_5_flash(config, request)

        return None

    async def _search_with_gemini_2_flash(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام Gemini 2.0 Flash مع البحث على الويب"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-goog-api-key': config.api_key
            }

            # إنشاء prompt للبحث العميق
            search_prompt = f"""
            أنت خبير في البحث عن أخبار الألعاب. ابحث عن معلومات حديثة ومفصلة حول: {request.query}

            يرجى تقديم:
            1. ملخص شامل للموضوع
            2. أحدث التطورات والأخبار
            3. مصادر موثوقة
            4. تحليل عميق للمعلومات

            استخدم البحث على الويب للحصول على أحدث المعلومات.
            """

            payload = {
                "contents": [{
                    "parts": [{"text": search_prompt}]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 2048,
                },
                "tools": [{
                    "googleSearchRetrieval": {
                        "dynamicRetrievalConfig": {
                            "mode": "MODE_DYNAMIC",
                            "dynamicThreshold": 0.7
                        }
                    }
                }] if request.include_web_search else []
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0]['content']['parts'][0]['text']

                            return {
                                'success': True,
                                'content': content,
                                'source': 'Gemini 2.5 Pro',
                                'model_type': 'gemini_2.5_pro',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ Gemini 2.5 Pro: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في Gemini 2.5 Pro: {e}")
            return None

    async def _search_with_deepseek_r1(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام DeepSeek R1 مع التفكير العميق"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {config.api_key}'
            }

            # إنشاء prompt للتفكير العميق والبحث
            deep_thinking_prompt = f"""
            أنت محلل خبير في أخبار الألعاب مع قدرات تفكير عميق وبحث على الإنترنت.

            المهمة: ابحث وحلل بعمق الموضوع التالي: {request.query}

            استخدم التفكير العميق لـ:
            1. تحليل السياق والخلفية
            2. البحث عن أحدث المعلومات
            3. ربط المعلومات ببعضها البعض
            4. تقديم رؤى عميقة ومفيدة

            قدم تحليلاً شاملاً ومفصلاً مع مصادر موثوقة.
            """

            payload = {
                "model": "deepseek-reasoner",
                "messages": [
                    {
                        "role": "user",
                        "content": deep_thinking_prompt
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 2048,
                "stream": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'choices' in data and data['choices']:
                            content = data['choices'][0]['message']['content']

                            return {
                                'success': True,
                                'content': content,
                                'source': 'DeepSeek R1',
                                'model_type': 'deepseek_r1',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query,
                                'reasoning_included': True
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ DeepSeek R1: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في DeepSeek R1: {e}")
            return None

    async def _search_with_groq(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام Groq API للمعالجة السريعة"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {config.api_key}'
            }

            # إنشاء prompt للبحث السريع
            fast_search_prompt = f"""
            أنت محرك بحث سريع ودقيق لأخبار الألعاب.

            ابحث بسرعة عن: {request.query}

            قدم:
            1. ملخص سريع ودقيق
            2. أهم النقاط والتطورات
            3. معلومات حديثة ومفيدة
            4. مصادر موثوقة

            ركز على السرعة والدقة.
            """

            payload = {
                "model": "llama-3.1-70b-versatile",
                "messages": [
                    {
                        "role": "user",
                        "content": fast_search_prompt
                    }
                ],
                "temperature": 0.5,
                "max_tokens": 1024,
                "stream": False
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'choices' in data and data['choices']:
                            content = data['choices'][0]['message']['content']

                            return {
                                'success': True,
                                'content': content,
                                'source': 'Groq API',
                                'model_type': 'groq_api',
                                'supports_web_search': True,
                                'timestamp': datetime.now(),
                                'query': request.query,
                                'fast_processing': True
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ Groq API: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في Groq API: {e}")
            return None

    async def _search_with_gemini_1_5_flash(self, config: AIModelConfig, request: SearchRequest) -> Optional[Dict]:
        """البحث باستخدام Gemini 2.5 Pro كحل احتياطي نهائي"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-goog-api-key': config.api_key
            }

            # إنشاء prompt بسيط للبحث الاحتياطي
            fallback_prompt = f"""
            أنت مساعد ذكي متخصص في أخبار الألعاب.

            الموضوع: {request.query}

            قدم معلومات مفيدة حول هذا الموضوع بناءً على معرفتك:
            1. معلومات عامة حول الموضوع
            2. السياق والخلفية
            3. نقاط مهمة يجب معرفتها
            4. توقعات أو تحليلات مفيدة

            ملاحظة: هذا بحث احتياطي بدون اتصال بالإنترنت.
            """

            payload = {
                "contents": [{
                    "parts": [{"text": fallback_prompt}]
                }],
                "generationConfig": {
                    "temperature": 0.6,
                    "topK": 32,
                    "topP": 0.9,
                    "maxOutputTokens": 1024,
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(config.api_endpoint, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'candidates' in data and data['candidates']:
                            content = data['candidates'][0]['content']['parts'][0]['text']

                            return {
                                'success': True,
                                'content': content,
                                'source': 'Gemini 1.5 Flash',
                                'model_type': 'gemini_1_5_flash',
                                'supports_web_search': False,
                                'timestamp': datetime.now(),
                                'query': request.query,
                                'fallback_mode': True
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ خطأ Gemini 1.5 Flash: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"❌ خطأ في Gemini 1.5 Flash: {e}")
            return None

    def get_model_recommendations(self) -> Dict:
        """الحصول على توصيات حول أفضل النماذج للاستخدام"""
        recommendations = {
            'best_for_web_search': AIModelType.GEMINI_2_FLASH.value,
            'best_for_deep_analysis': AIModelType.DEEPSEEK_R1.value,
            'best_for_speed': AIModelType.GROQ_API.value,
            'most_reliable_fallback': AIModelType.GEMINI_1_5_FLASH.value,
            'current_status': {}
        }

        for model_type in AIModelType:
            config = self.models[model_type]
            status = self.model_status[model_type]

            recommendations['current_status'][model_type.value] = {
                'available': self._is_model_available(model_type),
                'quality_score': config.quality_score,
                'speed_score': config.speed_score,
                'daily_usage': status['daily_requests'],
                'max_daily': config.max_requests_per_day,
                'usage_percentage': (status['daily_requests'] / config.max_requests_per_day) * 100
            }

        return recommendations

    async def test_all_models(self) -> Dict:
        """اختبار جميع النماذج للتأكد من عملها"""
        test_request = SearchRequest(
            query="test gaming news",
            max_results=1,
            search_depth="standard"
        )

        test_results = {}

        for model_type in AIModelType:
            if not self._is_model_available(model_type):
                test_results[model_type.value] = {
                    'status': 'unavailable',
                    'reason': 'Model not available (rate limits, blacklisted, or no API key)'
                }
                continue

            try:
                logger.info(f"🧪 اختبار {self.models[model_type].name}...")
                start_time = time.time()

                result = await self._search_with_model(model_type, test_request)

                end_time = time.time()
                response_time = end_time - start_time

                if result and result.get('success'):
                    test_results[model_type.value] = {
                        'status': 'success',
                        'response_time': response_time,
                        'content_length': len(result.get('content', '')),
                        'supports_web_search': result.get('supports_web_search', False)
                    }
                else:
                    test_results[model_type.value] = {
                        'status': 'failed',
                        'reason': 'No valid response received'
                    }

            except Exception as e:
                test_results[model_type.value] = {
                    'status': 'error',
                    'reason': str(e)
                }

        return test_results

# إنشاء مثيل عام
fallback_ai_manager = FallbackAIManager()
